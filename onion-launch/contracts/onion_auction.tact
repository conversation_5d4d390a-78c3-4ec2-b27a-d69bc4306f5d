import "@stdlib/ownable";
import "@stdlib/stoppable";
import "@stdlib/deploy";
import "./user_purchase";

// Messages
message Purchase {
    amount: Int as coins;
    currency: Int as uint8; // 0=TON, 1=USDT
}

message StartAuction {
    start_time: Int as uint64;
    end_time: Int as uint64;
    soft_cap: Int as coins;
    hard_cap: Int as coins;
    initial_price: Int as coins;
}

message UpdateRound {
    new_price: Int as coins;
    round_number: Int as uint32;
}

// Structs
struct AuctionConfig {
    start_time: Int as uint64;
    end_time: Int as uint64;
    soft_cap: Int as coins;
    hard_cap: Int as coins;
    total_supply: Int as coins;
    refund_fee_percent: Int as uint8; // 5% = 5
}

// Main Auction Contract
contract OnionAuction with Ownable, Stoppable {
    
    // State variables
    owner: Address;
    stopped: Bool;
    
    auction_config: AuctionConfig;
    current_round: Int as uint32;
    current_price: Int as coins;
    total_raised: Int as coins;
    total_tokens_sold: Int as coins;
    auction_status: Int as uint8; // 0=pending, 1=active, 2=ended_success, 3=ended_failure
    
    // Maps
    user_purchases: map<Address, Address>; // user -> UserPurchase contract address
    purchase_count: Int as uint32;
    
    // Constants
    const ROUND_DURATION: Int = 3600; // 1 hour in seconds
    const PRICE_INCREMENT: Int = ton("0.01"); // 0.01 TON per round
    const MIN_PURCHASE: Int = ton("50"); // 50 TON minimum
    
    init(
        owner: Address,
        start_time: Int,
        end_time: Int,
        soft_cap: Int,
        hard_cap: Int,
        total_supply: Int
    ) {
        self.owner = owner;
        self.stopped = false;
        
        self.auction_config = AuctionConfig{
            start_time: start_time,
            end_time: end_time,
            soft_cap: soft_cap,
            hard_cap: hard_cap,
            total_supply: total_supply,
            refund_fee_percent: 5
        };
        
        self.current_round = 1;
        self.current_price = ton("0.1"); // Initial price 0.1 TON per token
        self.total_raised = 0;
        self.total_tokens_sold = 0;
        self.auction_status = 0; // pending
        self.purchase_count = 0;
    }
    
    
    // Deploy message handler
    receive("Deploy") {
        // Handle deployment - no special logic needed for this contract
    }
    
    // Start auction
    receive(msg: StartAuction) {
        self.requireOwner();
        require(self.auction_status == 0, "Auction already started");
        
        self.auction_config.start_time = msg.start_time;
        self.auction_config.end_time = msg.end_time;
        self.auction_config.soft_cap = msg.soft_cap;
        self.auction_config.hard_cap = msg.hard_cap;
        self.current_price = msg.initial_price;
        self.auction_status = 1; // active
    }
    
    // Purchase tokens
    receive(msg: Purchase) {
        self.requireNotStopped();
        require(self.auction_status == 1, "Auction not active");
        require(now() >= self.auction_config.start_time, "Auction not started");
        require(now() <= self.auction_config.end_time, "Auction ended");
        require(msg.amount >= self.MIN_PURCHASE, "Amount below minimum");
        
        let current_time: Int = now();
        self.updateCurrentRound(current_time);
        
        // Calculate tokens to receive
        let tokens_to_receive: Int = (msg.amount * ton("1")) / self.current_price;
        
        require(self.total_tokens_sold + tokens_to_receive <= self.auction_config.total_supply, "Insufficient tokens available");
        
        // Update state
        self.total_raised += msg.amount;
        self.total_tokens_sold += tokens_to_receive;
        self.purchase_count += 1;
        
        // Check if hard cap reached
        if (self.total_raised >= self.auction_config.hard_cap) {
            self.auction_status = 2; // ended_success
        }
        
        // Create or update user purchase contract
        let user_purchase_addr: Address? = self.user_purchases.get(sender());
        if (user_purchase_addr == null) {
            // Create new user purchase contract
            let init_code: StateInit = self.getUserPurchaseInit(sender());
            let new_purchase_addr: Address = contractAddress(init_code);
            self.user_purchases.set(sender(), new_purchase_addr);
            
            send(SendParameters{
                to: new_purchase_addr,
                value: ton("0.2"), // Gas for contract deployment
                mode: SendIgnoreErrors,
                bounce: false,
                body: CreateUserPurchase{
                    user: sender(),
                    amount: msg.amount,
                    tokens: tokens_to_receive
                }.toCell(),
                code: init_code.code,
                data: init_code.data
            });
        } else {
            // Update existing user purchase contract
            send(SendParameters{
                to: user_purchase_addr!!,
                value: ton("0.05"),
                mode: SendIgnoreErrors,
                bounce: false,
                body: CreateUserPurchase{
                    user: sender(),
                    amount: msg.amount,
                    tokens: tokens_to_receive
                }.toCell()
            });
        }
    }
    
    // Process refund request
    receive(msg: ProcessRefund) {
        self.requireNotStopped();
        require(self.auction_status == 1, "Auction not active");
        require(now() <= self.auction_config.end_time, "Auction ended");
        
        let user_purchase_addr: Address? = self.user_purchases.get(msg.user);
        require(user_purchase_addr != null, "No purchase found");
        require(user_purchase_addr!! == sender(), "Unauthorized refund request");
        
        let fee: Int = (msg.amount * self.auction_config.refund_fee_percent) / 100;
        let refund_amount: Int = msg.amount - fee;
        
        // Update state
        self.total_raised -= msg.amount;
        
        // Send refund to user
        send(SendParameters{
            to: msg.user,
            value: refund_amount,
            mode: SendIgnoreErrors,
            bounce: false,
            body: "Refund processed".asComment()
        });
    }
    
    // End auction
    receive("end_auction") {
        require(now() > self.auction_config.end_time || self.total_raised >= self.auction_config.hard_cap, "Auction still active");
        
        if (self.total_raised >= self.auction_config.soft_cap) {
            self.auction_status = 2; // ended_success
        } else {
            self.auction_status = 3; // ended_failure
        }
    }
    
    // Update current round based on time
    fun updateCurrentRound(current_time: Int) {
        let elapsed_time: Int = current_time - self.auction_config.start_time;
        let new_round: Int = (elapsed_time / self.ROUND_DURATION) + 1;
        
        if (new_round > self.current_round) {
            self.current_round = new_round;
            self.current_price += self.PRICE_INCREMENT * (new_round - self.current_round);
        }
    }
    
    // Get user purchase contract init
    fun getUserPurchaseInit(user: Address): StateInit {
        return initOf UserPurchase(myAddress(), user);
    }
    
    // Getters
    get fun auction_info(): AuctionConfig {
        return self.auction_config;
    }
    
    get fun current_round(): Int {
        return self.current_round;
    }
    
    get fun current_price(): Int {
        return self.current_price;
    }
    
    get fun total_raised(): Int {
        return self.total_raised;
    }
    
    get fun total_tokens_sold(): Int {
        return self.total_tokens_sold;
    }
    
    get fun auction_status(): Int {
        return self.auction_status;
    }
    
    get fun purchase_count(): Int {
        return self.purchase_count;
    }
    
    get fun user_purchase_address(user: Address): Address? {
        return self.user_purchases.get(user);
    }
    
    get fun remaining_tokens(): Int {
        return self.auction_config.total_supply - self.total_tokens_sold;
    }
    
    get fun is_auction_active(): Bool {
        let current_time: Int = now();
        return self.auction_status == 1 && 
               current_time >= self.auction_config.start_time && 
               current_time <= self.auction_config.end_time;
    }
}
