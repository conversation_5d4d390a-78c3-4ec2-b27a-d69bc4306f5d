# OnionAuction USDT 集成实现总结

## 🎯 实现目标

为 OnionAuction 合约添加 USDT 支持，使用户可以使用 USDT（Jetton 标准代币）购买代币，同时保持与现有 TON 购买功能的兼容性。

## ✅ 完成的功能

### 1. 合约层面修改

#### 主合约 (onion_auction.tact)
- ✅ 添加了 `@stdlib/jetton` 导入
- ✅ 新增 `SetUSDTAddress` 消息类型
- ✅ 新增 `USDTConfig` 结构体
- ✅ 添加 USDT 相关状态变量：
  - `usdt_config: USDTConfig?`
  - `total_raised_usdt: Int`
- ✅ 实现 `JettonTransferNotification` 处理器
- ✅ 更新退款逻辑支持 USDT
- ✅ 添加新的 getter 方法：
  - `usdt_config()`
  - `total_raised_usdt()`
  - `total_raised_equivalent()`
  - `is_usdt_enabled()`

#### 用户购买合约 (user_purchase.tact)
- ✅ 更新 `CreateUserPurchase` 消息添加 `currency` 字段
- ✅ 更新 `ProcessRefund` 消息添加 `currency` 字段
- ✅ 修改购买记录存储以包含货币类型
- ✅ 更新退款逻辑处理不同货币类型

### 2. 核心功能实现

#### USDT 配置
- ✅ 合约所有者可以设置 USDT 主合约地址
- ✅ 配置拍卖合约的 USDT 钱包地址
- ✅ 支持 USDT 的 6 位小数精度

#### USDT 购买流程
- ✅ 通过 Jetton 转账通知机制接收 USDT
- ✅ 验证转账来源的 USDT 钱包地址
- ✅ 自动转换 USDT 金额到 TON 等价单位（6位小数转9位小数）
- ✅ 使用相同的代币价格计算
- ✅ 更新 USDT 筹集总额

#### 安全验证
- ✅ 验证 USDT 转账来自正确的 Jetton 钱包
- ✅ 检查最小购买金额要求
- ✅ 防止来自未授权钱包的转账
- ✅ 维护拍卖状态和时间限制

#### 退款支持
- ✅ 支持 USDT 退款通过 Jetton 转账
- ✅ 计算 5% 退款手续费
- ✅ 更新相应的筹集金额统计
- ✅ 发送退款通知消息

### 3. 文档和示例

#### 集成指南
- ✅ 创建了 `USDT_INTEGRATION.md` 详细说明
- ✅ 包含配置步骤和使用示例
- ✅ 安全考虑和最佳实践
- ✅ 测试示例代码

#### 测试文件
- ✅ 创建了 `OnionAuction.usdt.spec.ts` 测试文件
- ✅ 包含 USDT 配置测试
- ✅ USDT 购买流程测试
- ✅ 安全验证测试
- ✅ 混合货币购买测试

#### 部署脚本
- ✅ 创建了 `deployOnionAuctionWithUSDT.ts`
- ✅ 包含 USDT 配置步骤
- ✅ 主网和测试网地址配置
- ✅ 部署后配置指南

#### 使用示例
- ✅ 创建了 `usdt-purchase-example.ts`
- ✅ 完整的交互示例代码
- ✅ TON 和 USDT 购买对比
- ✅ 用户购买查询和退款示例

## 🔧 技术实现细节

### 价格计算
```tact
// USDT 转 TON 等价单位（6位小数转9位小数）
let usdt_amount_in_ton_units: Int = msg.amount * 1000;

// 使用相同价格计算代币数量
let tokens_to_receive: Int = (usdt_amount_in_ton_units * ton("1")) / self.current_price;
```

### 地址验证
```tact
// 验证 USDT 转账来源
let expected_wallet: Address = self.getJettonWalletAddress(usdt_config.master_address, myAddress());
require(sender() == expected_wallet, "Invalid USDT wallet");
```

### 退款处理
```tact
// USDT 退款通过 Jetton 转账
send(SendParameters{
    to: usdt_config.wallet_address!!,
    value: ton("0.1"),
    body: JettonTransfer{
        amount: refund_amount,
        destination: msg.user,
        forward_ton_amount: 1,
        forward_payload: "USDT refund processed".asComment()
    }.toCell()
});
```

## 📊 支持的功能对比

| 功能 | TON | USDT |
|------|-----|------|
| 购买代币 | ✅ | ✅ |
| 最小购买金额 | ✅ | ✅ |
| 价格计算 | ✅ | ✅ (自动转换) |
| 退款 | ✅ | ✅ |
| 手续费 | ✅ | ✅ |
| 购买记录 | ✅ | ✅ |
| 实时统计 | ✅ | ✅ |

## 🚀 部署流程

1. **部署合约**
   ```bash
   npx blueprint build
   npx blueprint run deployOnionAuctionWithUSDT
   ```

2. **配置 USDT**
   ```typescript
   // 获取拍卖合约的 USDT 钱包地址
   const usdtWalletAddress = await usdtMaster.getWalletAddress(auctionAddress);
   
   // 设置 USDT 配置
   await auction.send(owner, { value: toNano('0.1') }, {
       $$type: 'SetUSDTAddress',
       usdt_master: usdtMasterAddress,
       usdt_wallet: usdtWalletAddress
   });
   ```

3. **开始拍卖**
   ```typescript
   await auction.send(owner, { value: toNano('0.1') }, {
       $$type: 'StartAuction',
       start_time: startTime,
       end_time: endTime,
       soft_cap: softCap,
       hard_cap: hardCap,
       initial_price: initialPrice
   });
   ```

## 🔒 安全考虑

1. **地址验证**: 严格验证 USDT 转账来源
2. **金额验证**: 检查最小购买金额和供应量限制
3. **状态检查**: 确保拍卖处于活跃状态
4. **时间验证**: 验证拍卖时间窗口
5. **权限控制**: 只有合约所有者可以配置 USDT

## 📝 待完善项目

1. **Jetton 钱包地址计算**: 当前使用简化实现，生产环境需要调用 USDT 主合约的 `get_wallet_address` 方法
2. **Gas 优化**: 可以进一步优化 USDT 操作的 Gas 消耗
3. **错误处理**: 增加更详细的错误消息和处理逻辑
4. **前端集成**: 更新前端界面支持 USDT 购买选项

## 🎉 总结

成功为 OnionAuction 合约添加了完整的 USDT 支持，包括：
- 双货币购买系统
- 统一的价格计算机制
- 安全的地址验证
- 完整的退款支持
- 详细的文档和测试

该实现遵循 TON 区块链的 TEP-74 Jetton 标准，确保与生态系统的兼容性和安全性。
